<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Table Tree Test</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        .container { width: 95%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; }
        
        /* 自定义任务颜色 */
        .bar-project .bar { fill: #4f8cff !important; }
        .bar-task .bar { fill: #34c759 !important; }
        .bar-subtask .bar { fill: #ff9500 !important; }
        .bar-milestone .bar { fill: #ff3b30 !important; }
        
        /* 进度条颜色 */
        .bar-project .bar-progress { fill: #1e40af !important; }
        .bar-task .bar-progress { fill: #15803d !important; }
        .bar-subtask .bar-progress { fill: #b45309 !important; }
        .bar-milestone .bar-progress { fill: #dc2626 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1 class="text-center pt-4 pb-2">Table Tree Test</h1>
    
    <div class="row mb-3">
        <div class="col-12">
            <button id="expand-all-btn">展开所有</button>
            <button id="collapse-all-btn">折叠所有</button>
            <button id="toggle-table-btn">切换表格显示</button>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="chart" id="test-gantt"></div>
        </div>
    </div>
</div>

<script>
    console.log('Starting table tree test...');
    
    const today = new Date();
    const daysSince = (dx) => new Date(today.getTime() + dx * 86400000);
    
    // 简化的测试数据 - 使用字符串日期格式
    const testData = [
        {
            id: 'Project1',
            name: 'Project #1',
            start: '2023-04-02',
            end: '2023-05-04',
            progress: 45,
            custom_class: 'bar-project',
            priority: 'Medium'
        },
        {
            id: 'Task1',
            name: 'Task #1',
            start: '2023-04-03',
            end: '2023-04-08',
            progress: 80,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2',
            name: 'Task #2',
            start: '2023-04-03',
            end: '2023-04-14',
            progress: 30,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2_1',
            name: 'Task #2.1',
            start: '2023-04-03',
            end: '2023-04-06',
            progress: 100,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Medium'
        },
        {
            id: 'Task2_2',
            name: 'Task #2.2',
            start: '2023-04-06',
            end: '2023-04-09',
            progress: 60,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        }
    ];
    
    console.log('Test data:', testData);
    
    try {
        // 初始化甘特图
        const gantt = new Gantt('#test-gantt', testData, {
            view_mode: 'Day',
            bar_height: 32,
            bar_corner_radius: 8,
            arrow_curve: 8,
            padding: 20,
            // 启用表格树功能
            table_tree: {
                enabled: true,
                show_table: true,
                table_width: 300,
                indent_width: 20,
                expand_icon: '▶',
                collapse_icon: '▼',
                columns: [
                    { key: 'name', title: 'Task name', width: 150 },
                    { key: 'start', title: 'Start time', width: 80 },
                    { key: 'duration', title: 'Duration', width: 70 }
                ]
            }
        });
        
        console.log('Gantt initialized successfully:', gantt);
        
        // 绑定按钮事件
        document.getElementById('expand-all-btn').onclick = () => {
            console.log('Expanding all...');
            gantt.expand_all();
        };
        
        document.getElementById('collapse-all-btn').onclick = () => {
            console.log('Collapsing all...');
            gantt.collapse_all();
        };
        
        document.getElementById('toggle-table-btn').onclick = () => {
            console.log('Toggling table...');
            const currentShow = gantt.options.table_tree.show_table;
            gantt.options.table_tree.show_table = !currentShow;
            gantt.render();
        };
        
    } catch (error) {
        console.error('Error initializing gantt:', error);
    }
</script>
</body>
</html>
