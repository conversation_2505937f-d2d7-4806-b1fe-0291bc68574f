{"name": "frappe-gantt", "version": "1.0.3", "description": "A simple, modern, interactive gantt library for the web", "main": "src/index.js", "type": "module", "scripts": {"dev": "vite", "build-dev": "vite build --watch", "build": "vite build", "lint": "eslint src/**/*.js", "prettier": "prettier --write \"{src/*,tests/*,rollup.config}.js\"", "prettier-check": "prettier --check \"{src/*,tests/*,rollup.config}.js\""}, "repository": {"type": "git", "url": "git+https://github.com/frappe/gantt.git"}, "files": ["src", "dist", "README.md"], "exports": {".": {"require": "./dist/frappe-gantt.umd.js", "import": "./dist/frappe-gantt.es.js", "style": "./dist/frappe-gantt.css"}}, "keywords": ["gantt", "svg", "simple gantt", "project timeline", "interactive gantt", "project management"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/frappe/gantt/issues"}, "homepage": "https://github.com/frappe/gantt", "devDependencies": {"eslint": "^9.15.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-prettier": "^2.6.0", "postcss-nesting": "^12.1.2", "prettier": "3.2.5", "vite": "^5.2.10"}, "eslintIgnore": ["dist"], "sideEffects": ["*.css"], "packageManager": "pnpm@10.12.4+sha512.5ea8b0deed94ed68691c9bad4c955492705c5eeb8a87ef86bc62c74a26b037b08ff9570f108b2e4dbd1dd1a9186fea925e527f141c648e85af45631074680184"}