<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <title>甘特图案例</title>
  <link rel="stylesheet" href="../dist/frappe-gantt.css" />
  <script src="../dist/frappe-gantt.umd.js"></script>
  <style>
    body { font-family: Arial, sans-serif; }
    .container { display: flex; }
    .tree-table {
      border-collapse: collapse;
      min-width: 350px;
      max-width: 400px;
      margin-right: 20px;
      font-size: 14px;
    }
    .tree-table th, .tree-table td {
      border: 1px solid #ddd;
      padding: 6px 8px;
    }
    .tree-table th {
      background: #f5f5f5;
    }
    .tree-indent {
      display: inline-block;
      width: 18px;
    }
    .tree-toggle {
      cursor: pointer;
      margin-right: 4px;
    }
    .priority-high { color: #d9534f; }
    .priority-medium { color: #f0ad4e; }
    .priority-low { color: #5bc0de; }
    #gantt { flex: 1; }
  </style>
</head>
<body>
  <h2>甘特图案例</h2>
  <div class="container">
    <table class="tree-table" id="task-table">
      <thead>
        <tr>
          <th>Task name</th>
          <th>Start time</th>
          <th>Priority</th>
        </tr>
      </thead>
      <tbody>
        <!-- 表格内容由JS生成 -->
      </tbody>
    </table>
    <div id="gantt"></div>
  </div>
  <script src="https://unpkg.com/frappe-gantt/dist/frappe-gantt.min.js"></script>
  <script>
    // 任务数据
    const tasks = [
      { id: '1', name: 'Project #1', start: '2023-04-01', end: '2023-04-10', priority: 'Medium', parent: null },
      { id: '1.1', name: 'Task #1', start: '2023-04-03', end: '2023-04-08', priority: 'High', parent: '1' },
      { id: '1.2', name: 'Task #2', start: '2023-04-02', end: '2023-04-09', priority: 'High', parent: '1' },
      { id: '1.2.1', name: 'Task #2.1', start: '2023-04-03', end: '2023-04-04', priority: 'Medium', parent: '1.2' },
      { id: '1.2.2', name: 'Task #2.2', start: '2023-04-06', end: '2023-04-09', priority: 'Low', parent: '1.2' },
      { id: '1.2.3', name: 'Task #2.3', start: '2023-04-10', end: '2023-04-11', priority: 'High', parent: '1.2' },
      { id: '1.2.4', name: 'Task #2.4', start: '2023-04-10', end: '2023-04-11', priority: 'High', parent: '1.2' },
      { id: '3', name: 'Task #3', start: '2023-04-02', end: '2023-04-08', priority: 'Medium', parent: null },
      { id: '4', name: 'Task #4', start: '2023-04-02', end: '2023-04-07', priority: 'Low', parent: null },
      { id: '4.1', name: 'Task #4.1', start: '2023-04-03', end: '2023-04-07', priority: 'Low', parent: '4' },
      { id: '4.2', name: 'Task #4.2', start: '2023-04-03', end: '2023-04-07', priority: 'Low', parent: '4' },
      { id: '4.3', name: 'Task #4.3', start: '2023-04-03', end: '2023-04-07', priority: 'Low', parent: '4' },
      { id: '5', name: 'Task #5', start: '2023-04-02', end: '2023-04-09', priority: 'Medium', parent: null }
    ];

    // 构建树形结构
    function buildTree(tasks) {
      const map = {};
      tasks.forEach(t => map[t.id] = { ...t, children: [] });
      const roots = [];
      tasks.forEach(t => {
        if (t.parent) {
          map[t.parent].children.push(map[t.id]);
        } else {
          roots.push(map[t.id]);
        }
      });
      return roots;
    }

    // 渲染表格
    function renderTable(tree, level = 0, tbody = document.querySelector('#task-table tbody')) {
      tree.forEach(node => {
        const tr = document.createElement('tr');
        // 名称及树形缩进
        const tdName = document.createElement('td');
        for (let i = 0; i < level; i++) {
          tdName.appendChild(document.createElement('span')).className = 'tree-indent';
        }
        if (node.children.length > 0) {
          const toggle = document.createElement('span');
          toggle.textContent = '▸';
          toggle.className = 'tree-toggle';
          toggle.onclick = function() {
            const next = tr.nextSibling;
            let show = toggle.textContent === '▸';
            toggle.textContent = show ? '▼' : '▸';
            let sibling = next;
            while (sibling && sibling.dataset && sibling.dataset.parent === node.id) {
              sibling.style.display = show ? '' : 'none';
              // 如果收起，还要递归收起子节点
              if (!show && sibling.querySelector('.tree-toggle')) {
                sibling.querySelector('.tree-toggle').textContent = '▸';
              }
              sibling = sibling.nextSibling;
            }
          };
          tdName.appendChild(toggle);
        } else {
          tdName.appendChild(document.createElement('span')).className = 'tree-indent';
        }
        tdName.appendChild(document.createTextNode(node.name));
        tr.appendChild(tdName);

        // 开始时间
        const tdStart = document.createElement('td');
        tdStart.textContent = node.start;
        tr.appendChild(tdStart);

        // 优先级
        const tdPriority = document.createElement('td');
        tdPriority.textContent = node.priority;
        tdPriority.className = 'priority-' + node.priority.toLowerCase();
        tr.appendChild(tdPriority);

        if (node.parent) tr.dataset.parent = node.parent;
        tbody.appendChild(tr);

        if (node.children.length > 0) {
          renderTable(node.children, level + 1, tbody);
        }
      });
    }

    // 初始化表格
    const tree = buildTree(tasks);
    renderTable(tree);

    // 展开所有根节点
    document.querySelectorAll('.tree-toggle').forEach(toggle => toggle.click());

    // 甘特图数据
    const ganttTasks = tasks.map(t => ({
      id: t.id,
      name: t.name,
      start: t.start,
      end: t.end,
      progress: 100,
      dependencies: '',
      custom_class: t.priority ? 'priority-' + t.priority.toLowerCase() : ''
    }));

    // 计算最早开始和最晚结束日期
    function getMinMaxDate(tasks) {
      const starts = tasks.map(t => new Date(t.start));
      const ends = tasks.map(t => new Date(t.end));
      const min = new Date(Math.min(...starts));
      const max = new Date(Math.max(...ends));
      return { min, max };
    }
    const { min: minDate, max: maxDate } = getMinMaxDate(tasks);

    // 渲染甘特图
    const gantt = new Gantt("#gantt", ganttTasks, {
      view_mode: 'Day',
      custom_popup_html: null
    });

    // 自动滚动到最早任务日期，避免大量无效空白
    setTimeout(() => {
      if (gantt && gantt.scroll_to_date) {
        gantt.scroll_to_date(minDate);
      }
    }, 300);
  </script>
</body>
</html>