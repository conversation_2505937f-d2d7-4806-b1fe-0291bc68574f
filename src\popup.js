
// 弹窗类，负责任务详情弹窗的生成与显示
export default class Popup {
    /**
     * 构造函数
     * @param {HTMLElement} parent - 弹窗容器
     * @param {Function} popup_func - 弹窗内容生成函数
     * @param {Gantt} gantt - 甘特图实例
     */
    constructor(parent, popup_func, gantt) {
        this.parent = parent;
        this.popup_func = popup_func;
        this.gantt = gantt;
        this.make(); // 初始化弹窗结构
    }


    /**
     * 创建弹窗DOM结构
     */
    make() {
        this.parent.innerHTML = `
            <div class="title"></div>
            <div class="subtitle"></div>
            <div class="details"></div>
            <div class="actions"></div>
        `;
        this.hide();

        this.title = this.parent.querySelector('.title');
        this.subtitle = this.parent.querySelector('.subtitle');
        this.details = this.parent.querySelector('.details');
        this.actions = this.parent.querySelector('.actions');
    }


    /**
     * 显示弹窗
     * @param {Object} param0 - 弹窗参数（x, y, task, target）
     */
    show({ x, y, task, target }) {
        this.actions.innerHTML = '';
        // 调用自定义弹窗内容生成函数
        let html = this.popup_func({
            task,
            chart: this.gantt,
            get_title: () => this.title,
            set_title: (title) => (this.title.innerHTML = title),
            get_subtitle: () => this.subtitle,
            set_subtitle: (subtitle) => (this.subtitle.innerHTML = subtitle),
            get_details: () => this.details,
            set_details: (details) => (this.details.innerHTML = details),
            add_action: (html, func) => {
                let action = this.gantt.create_el({
                    classes: 'action-btn',
                    type: 'button',
                    append_to: this.actions,
                });
                if (typeof html === 'function') html = html(task);
                action.innerHTML = html;
                action.onclick = (e) => func(task, this.gantt, e);
            },
        });
        if (html === false) return;
        if (html) this.parent.innerHTML = html;

        // 如果没有actions则移除，否则追加
        if (this.actions.innerHTML === '') this.actions.remove();
        else this.parent.appendChild(this.actions);

        // 设置弹窗位置
        this.parent.style.left = x + 10 + 'px';
        this.parent.style.top = y - 10 + 'px';
        this.parent.classList.remove('hide');
    }

    /**
     * 隐藏弹窗
     */
    hide() {
        this.parent.classList.add('hide');
    }
}
