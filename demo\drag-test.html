<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Drag and Position Test</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        .container { width: 95%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; height: 500px; }
        .controls { margin: 20px 0; }
        .controls button { margin-right: 10px; padding: 8px 16px; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
        .instructions { margin: 10px 0; padding: 10px; background: #e3f2fd; border-radius: 4px; }
        
        /* 自定义任务颜色 */
        .bar-project .bar { fill: #4f8cff !important; }
        .bar-task .bar { fill: #34c759 !important; }
        .bar-subtask .bar { fill: #ff9500 !important; }
        
        /* 进度条颜色 */
        .bar-project .bar-progress { fill: #1e40af !important; }
        .bar-task .bar-progress { fill: #15803d !important; }
        .bar-subtask .bar-progress { fill: #b45309 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1>Drag and Position Preservation Test</h1>
    
    <div class="instructions">
        <h3>Test Instructions:</h3>
        <ol>
            <li>Drag any task to a new position</li>
            <li>Click "Collapse All" - tasks should maintain their new positions</li>
            <li>Click "Expand All" - tasks should still be in their dragged positions</li>
            <li>Try dragging tasks after collapsing - no errors should occur</li>
        </ol>
    </div>
    
    <div class="status" id="status">
        Status: Ready for testing
    </div>
    
    <div class="controls">
        <button id="expand-all-btn">展开所有</button>
        <button id="collapse-all-btn">折叠所有</button>
        <button id="reset-btn">重置位置</button>
        <button id="log-positions-btn">记录当前位置</button>
    </div>
    
    <div class="chart" id="drag-test"></div>
</div>

<script>
    let gantt;
    let originalPositions = {};
    
    function updateStatus(message) {
        document.getElementById('status').textContent = 'Status: ' + message;
        console.log('Status:', message);
    }
    
    function logTaskPositions() {
        const positions = {};
        gantt.tasks.forEach(task => {
            positions[task.id] = {
                start: task.start,
                end: task.end,
                _start: task._start,
                _end: task._end
            };
        });
        console.log('Current task positions:', positions);
        return positions;
    }
    
    // 测试数据
    const testData = [
        {
            id: 'Project1',
            name: 'Project #1',
            start: '2023-04-02',
            end: '2023-05-04',
            progress: 45,
            custom_class: 'bar-project',
            priority: 'Medium'
        },
        {
            id: 'Task1',
            name: 'Task #1',
            start: '2023-04-03',
            end: '2023-04-08',
            progress: 80,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2',
            name: 'Task #2',
            start: '2023-04-03',
            end: '2023-04-14',
            progress: 30,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2_1',
            name: 'Task #2.1',
            start: '2023-04-03',
            end: '2023-04-06',
            progress: 100,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Medium'
        },
        {
            id: 'Task2_2',
            name: 'Task #2.2',
            start: '2023-04-06',
            end: '2023-04-09',
            progress: 60,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        }
    ];
    
    try {
        updateStatus('Initializing Gantt chart...');
        
        // 保存原始位置
        originalPositions = {};
        testData.forEach(task => {
            originalPositions[task.id] = {
                start: task.start,
                end: task.end
            };
        });
        
        // 初始化甘特图
        gantt = new Gantt('#drag-test', testData, {
            view_mode: 'Day',
            bar_height: 32,
            bar_corner_radius: 8,
            arrow_curve: 8,
            padding: 20,
            // 启用表格树功能
            table_tree: {
                enabled: true,
                show_table: true,
                table_width: 300,
                indent_width: 20,
                expand_icon: '▶',
                collapse_icon: '▼',
                columns: [
                    { key: 'name', title: 'Task name', width: 150 },
                    { key: 'start', title: 'Start time', width: 80 },
                    { key: 'duration', title: 'Duration', width: 70 }
                ]
            },
            // 事件回调
            on_date_change: function(task, start, end) {
                updateStatus(`Task ${task.name} moved to ${start.toDateString()}`);
                console.log(`Task ${task.name} moved:`, { start, end });
            }
        });
        
        updateStatus('Gantt chart initialized successfully. Try dragging tasks!');
        
    } catch (error) {
        updateStatus('Error: ' + error.message);
        console.error('Initialization error:', error);
    }
    
    // 绑定按钮事件
    document.getElementById('expand-all-btn').onclick = () => {
        try {
            updateStatus('Expanding all tasks...');
            gantt.expand_all();
            updateStatus('All tasks expanded');
        } catch (error) {
            updateStatus('Expand failed: ' + error.message);
            console.error('Expand error:', error);
        }
    };
    
    document.getElementById('collapse-all-btn').onclick = () => {
        try {
            updateStatus('Collapsing all tasks...');
            gantt.collapse_all();
            updateStatus('All tasks collapsed');
        } catch (error) {
            updateStatus('Collapse failed: ' + error.message);
            console.error('Collapse error:', error);
        }
    };
    
    document.getElementById('reset-btn').onclick = () => {
        try {
            updateStatus('Resetting task positions...');
            // 重置任务位置到原始值
            gantt.tasks.forEach(task => {
                if (originalPositions[task.id]) {
                    task.start = originalPositions[task.id].start;
                    task.end = originalPositions[task.id].end;
                    task._start = new Date(task.start);
                    task._end = new Date(task.end);
                }
            });
            gantt.render();
            updateStatus('Task positions reset to original');
        } catch (error) {
            updateStatus('Reset failed: ' + error.message);
            console.error('Reset error:', error);
        }
    };
    
    document.getElementById('log-positions-btn').onclick = () => {
        updateStatus('Logging current positions to console...');
        logTaskPositions();
        updateStatus('Positions logged to console');
    };
</script>
</body>
</html>
