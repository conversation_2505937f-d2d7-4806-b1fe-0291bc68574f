<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Frappe Gantt 表格树功能演示</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
        .container { width: 95%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; }
        
        /* 自定义任务颜色 */
        .bar-project .bar { fill: #4f8cff !important; }
        .bar-task .bar { fill: #34c759 !important; }
        .bar-subtask .bar { fill: #ff9500 !important; }
        
        /* 进度条颜色 */
        .bar-project .bar-progress { fill: #1e40af !important; }
        .bar-task .bar-progress { fill: #15803d !important; }
        .bar-subtask .bar-progress { fill: #b45309 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1 class="text-center pt-4 pb-2">Frappe Gantt 表格树功能演示</h1>
    <p class="text-center">本案例展示了表格树功能，支持层级结构、展开/折叠、树形显示等特性。</p>
    
    <div class="row mb-3">
        <div class="col-12">
            <button id="expand-all-btn" class="btn btn-outline-primary me-2">展开所有</button>
            <button id="collapse-all-btn" class="btn btn-outline-secondary me-2">折叠所有</button>
            <button id="toggle-table-btn" class="btn btn-outline-info">切换表格显示</button>
        </div>
    </div>
    
    <div class="row">
        <div class="col-12">
            <div class="chart" id="table-tree-demo"></div>
        </div>
    </div>
</div>

<script type="module">
    const rawToday = new Date();
    const today = Date.UTC(rawToday.getFullYear(), rawToday.getMonth(), rawToday.getDate()) + new Date().getTimezoneOffset() * 60000;
    const daysSince = (dx) => new Date(today + dx * 86400000);
    
    // 表格树测试数据
    const treeTasksData = [
        // 项目1
        {
            id: 'Project1',
            name: 'Project #1',
            start: daysSince(-5),
            end: daysSince(32),
            progress: 45,
            level: 0,
            parent: null,
            children: ['Task1', 'Task2'],
            custom_class: 'bar-project',
            priority: 'Medium'
        },
        // 任务1
        {
            id: 'Task1',
            name: 'Task #1',
            start: daysSince(-3),
            end: daysSince(8),
            progress: 80,
            level: 1,
            parent: 'Project1',
            children: [],
            custom_class: 'bar-task',
            priority: 'High'
        },
        // 任务2（有子任务）
        {
            id: 'Task2',
            name: 'Task #2',
            start: daysSince(-2),
            end: daysSince(18),
            progress: 30,
            level: 1,
            parent: 'Project1',
            children: ['Task2_1', 'Task2_2'],
            custom_class: 'bar-task',
            priority: 'High'
        },
        // 子任务2.1
        {
            id: 'Task2_1',
            name: 'Task #2.1',
            start: daysSince(-3),
            end: daysSince(3),
            progress: 100,
            level: 2,
            parent: 'Task2',
            children: [],
            custom_class: 'bar-subtask',
            priority: 'Medium'
        },
        // 子任务2.2
        {
            id: 'Task2_2',
            name: 'Task #2.2',
            start: daysSince(6),
            end: daysSince(12),
            progress: 60,
            level: 2,
            parent: 'Task2',
            children: [],
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        // 任务3
        {
            id: 'Task3',
            name: 'Task #3',
            start: daysSince(-2),
            end: daysSince(8),
            progress: 75,
            level: 0,
            parent: null,
            children: [],
            custom_class: 'bar-task',
            priority: 'Medium'
        },
        // 任务4（有多个子任务）
        {
            id: 'Task4',
            name: 'Task #4',
            start: daysSince(-2),
            end: daysSince(15),
            progress: 25,
            level: 0,
            parent: null,
            children: ['Task4_1', 'Task4_2', 'Task4_3'],
            custom_class: 'bar-task',
            priority: 'Low'
        },
        // 子任务4.1
        {
            id: 'Task4_1',
            name: 'Task #4.1',
            start: daysSince(-3),
            end: daysSince(3),
            progress: 90,
            level: 1,
            parent: 'Task4',
            children: [],
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        // 子任务4.2
        {
            id: 'Task4_2',
            name: 'Task #4.2',
            start: daysSince(-3),
            end: daysSince(3),
            progress: 70,
            level: 1,
            parent: 'Task4',
            children: [],
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        // 子任务4.3
        {
            id: 'Task4_3',
            name: 'Task #4.3',
            start: daysSince(-3),
            end: daysSince(8),
            progress: 50,
            level: 1,
            parent: 'Task4',
            children: [],
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        // 任务5
        {
            id: 'Task5',
            name: 'Task #5',
            start: daysSince(-2),
            end: daysSince(18),
            progress: 60,
            level: 0,
            parent: null,
            children: [],
            custom_class: 'bar-task',
            priority: 'Medium'
        }
    ];
    
    // 初始化甘特图
    let gantt = new Gantt('#table-tree-demo', treeTasksData, {
        view_mode: 'Day',
        view_mode_select: true,
        today_button: true,
        bar_height: 32,
        bar_corner_radius: 8,
        arrow_curve: 8,
        padding: 20,
        // 启用表格树功能
        table_tree: {
            enabled: true,
            show_table: true,
            table_width: 300,
            indent_width: 20,
            expand_icon: '▶',
            collapse_icon: '▼',
            columns: [
                { key: 'name', title: 'Task name', width: 150 },
                { key: 'start', title: 'Start time', width: 80 },
                { key: 'priority', title: 'Priority', width: 70 }
            ]
        },
        // 事件回调
        on_task_toggle: function(task, expanded) {
            console.log(`Task ${task.name} ${expanded ? 'expanded' : 'collapsed'}`);
        },
        on_expand_all: function() {
            console.log('All tasks expanded');
        },
        on_collapse_all: function() {
            console.log('All tasks collapsed');
        }
    });
    
    // 绑定按钮事件
    document.getElementById('expand-all-btn').onclick = () => {
        gantt.expand_all();
    };
    
    document.getElementById('collapse-all-btn').onclick = () => {
        gantt.collapse_all();
    };
    
    document.getElementById('toggle-table-btn').onclick = () => {
        const currentShow = gantt.options.table_tree.show_table;
        gantt.options.table_tree.show_table = !currentShow;
        gantt.render();
    };
</script>
</body>
</html>
