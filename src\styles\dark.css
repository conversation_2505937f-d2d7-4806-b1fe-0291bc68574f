:root {
    --g-bar-stroke-dark: #c6ccd2;
    --g-border-color-dark: #616161;
    --g-bar-color-dark: #616161;
    --g-bg-dark: #3e3e3e;
    --g-light-border-color-dark: #3e3e3e;
    --g-text-muted-dark: #eee;
    --g-text-light-dark: #ececec;
    --g-text-color-dark: #f7f7f7;
    --g-progress-color: #8a8aff;
}

.dark > .gantt-container .gantt {
    & .grid-row {
        fill: #252525;
    }

    & .row-line {
        stroke: var(--g-light-border-color-dark);
    }

    & .tick {
        stroke: var(--g-border-color-dark);
    }

    & .arrow {
        stroke: var(--g-text-muted-dark);
    }

    & .bar {
        fill: var(--g-bar-color-dark);
        stroke: none;
    }

    & .bar-progress {
        fill: var(--g-progress-color);
    }

    & .bar-invalid {
        fill: transparent;
        stroke: var(--g-bar-stroke-dark);

        & ~ .bar-label {
            fill: var(--g-text-light-dark);
        }
    }

    & .bar-label.big {
        fill: var(--g-text-light-dark);
    }

    & .bar-wrapper {
        &:hover {
            .bar {
                fill: lighten(var(--g-bar-color-dark, 5));
            }

            & .bar-progress {
                fill: lighten(var(--g-progress-color, 5));
            }
        }

        &.active {
            .bar {
                fill: lighten(var(--g-bar-color-dark, 5));
            }

            & .bar-progress {
                fill: lighten(var(--g-progress-color, 5));
            }
        }
    }
}

.dark > .gantt-container {
    & .grid-header {
        background-color: #252525;
    }

    & .popup-wrapper {
        background-color: #333;

        & .title {
            border-color: lighten(var(--g-progress-color, 5));
        }
    }
}
