
// 导入SVG创建工具函数
import { createSVG } from './svg_utils';


// 依赖箭头类，负责绘制任务之间的依赖连线
export default class Arrow {
    /**
     * @param {Gantt} gantt - 甘特图实例
     * @param {Bar} from_task - 起始任务Bar对象
     * @param {Bar} to_task - 目标任务Bar对象
     */
    constructor(gantt, from_task, to_task) {
        this.gantt = gantt;
        this.from_task = from_task;
        this.to_task = to_task;

        // 计算路径并绘制箭头
        this.calculate_path();
        this.draw();
    }

    /**
     * 计算箭头的SVG路径
     */
    calculate_path() {
        // 箭头起点x坐标（起始任务Bar的中点）
        let start_x =
            this.from_task.$bar.getX() + this.from_task.$bar.getWidth() / 2;

        // 如果目标任务在起始任务左侧且有重叠，起点左移，避免箭头穿过bar
        const condition = () =>
            this.to_task.$bar.getX() < start_x + this.gantt.options.padding &&
            start_x > this.from_task.$bar.getX() + this.gantt.options.padding;
        while (condition()) {
            start_x -= 10;
        }
        start_x -= 10;

        // 箭头起点y坐标（起始任务Bar的垂直中心）
        let start_y =
            this.gantt.config.header_height +
            this.gantt.options.bar_height +
            (this.gantt.options.padding + this.gantt.options.bar_height) *
                this.from_task.task._index +
            this.gantt.options.padding / 2;

        // 箭头终点x坐标（目标任务Bar左侧）
        let end_x = this.to_task.$bar.getX() - 13;
        // 箭头终点y坐标（目标任务Bar的垂直中心）
        let end_y =
            this.gantt.config.header_height +
            this.gantt.options.bar_height / 2 +
            (this.gantt.options.padding + this.gantt.options.bar_height) *
                this.to_task.task._index +
            this.gantt.options.padding / 2;

        // 判断依赖方向（上/下）
        const from_is_below_to =
            this.from_task.task._index > this.to_task.task._index;

        // 箭头弯曲度
        let curve = this.gantt.options.arrow_curve;
        const clockwise = from_is_below_to ? 1 : 0;
        let curve_y = from_is_below_to ? -curve : curve;

        // 如果目标任务在起始任务左侧，路径需要绕开bar
        if (
            this.to_task.$bar.getX() <=
            this.from_task.$bar.getX() + this.gantt.options.padding
        ) {
            let down_1 = this.gantt.options.padding / 2 - curve;
            if (down_1 < 0) {
                down_1 = 0;
                curve = this.gantt.options.padding / 2;
                curve_y = from_is_below_to ? -curve : curve;
            }
            const down_2 =
                this.to_task.$bar.getY() +
                this.to_task.$bar.getHeight() / 2 -
                curve_y;
            const left = this.to_task.$bar.getX() - this.gantt.options.padding;
            // 绘制折线路径，绕开bar
            this.path = `
                M ${start_x} ${start_y}
                v ${down_1}
                a ${curve} ${curve} 0 0 1 ${-curve} ${curve}
                H ${left}
                a ${curve} ${curve} 0 0 ${clockwise} ${-curve} ${curve_y}
                V ${down_2}
                a ${curve} ${curve} 0 0 ${clockwise} ${curve} ${curve_y}
                L ${end_x} ${end_y}
                m -5 -5
                l 5 5
                l -5 5`;
        } else {
            // 直接连线
            if (end_x < start_x + curve) curve = end_x - start_x;

            let offset = from_is_below_to ? end_y + curve : end_y - curve;

            this.path = `
              M ${start_x} ${start_y}
              V ${offset}
              a ${curve} ${curve} 0 0 ${clockwise} ${curve} ${curve}
              L ${end_x} ${end_y}
              m -5 -5
              l 5 5
              l -5 5`;
        }
    }


    /**
     * 绘制SVG箭头元素
     */
    draw() {
        this.element = createSVG('path', {
            d: this.path,
            'data-from': this.from_task.task.id,
            'data-to': this.to_task.task.id,
        });
    }

    /**
     * 更新箭头路径（如任务位置变化时）
     */
    update() {
        this.calculate_path();
        this.element.setAttribute('d', this.path);
    }
}
