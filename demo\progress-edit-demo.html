<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Frappe Gantt 进度调整案例</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet" />
    <style>
      .container { width: 90%; margin: 0 auto; }
      .chart { border: 1px dotted black; border-radius: 4px; height: fit-content; }
      .progress-input { width: 80px; display: inline-block; margin-left: 10px; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
  </head>
  <body>
    <div class="container">
      <h1 class="text-center pt-3 pb-2">Frappe Gantt - 进度调整案例</h1>
      <hr />
      <div class="row my-5">
        <div class="col-md-3 px-5 py-1">
          <h3 class="text-center">进度调整</h3>
          <p>通过输入框实时调整任务进度，甘特图自动更新。</p>
          <div id="progress-controls"></div>
        </div>
        <div class="chart col-md-9" id="progress-gantt"></div>
      </div>
    </div>
    <script type="module">
      const today = new Date();
      function daysSince(dx) {
        const d = new Date(today);
        d.setDate(d.getDate() + dx);
        return d;
      }
      let tasks = [
        { id: 'task-1', name: '需求分析', start: daysSince(-2), end: daysSince(2), progress: 30 },
        { id: 'task-2', name: '设计', start: daysSince(3), end: daysSince(7), progress: 50, dependencies: 'task-1' },
        { id: 'task-3', name: '开发', start: daysSince(8), end: daysSince(15), progress: 10, dependencies: 'task-2' },
        { id: 'task-4', name: '测试', start: daysSince(16), end: daysSince(20), progress: 0, dependencies: 'task-3' },
      ];
      const gantt = new Gantt('#progress-gantt', tasks, {
        readonly: false,
        readonly_dates: false,
        readonly_progress: false,
        view_mode: 'Day',
      });
      // 生成进度输入控件
      const controls = document.getElementById('progress-controls');
      tasks.forEach((task, idx) => {
        const div = document.createElement('div');
        div.className = 'mb-2';
        div.innerHTML = `<label>${task.name}：</label><input type="number" min="0" max="100" value="${task.progress}" class="progress-input" id="progress-input-${idx}" />%`;
        controls.appendChild(div);
        document.getElementById(`progress-input-${idx}`).addEventListener('input', (e) => {
          const val = Math.max(0, Math.min(100, Number(e.target.value)));
          gantt.update_task(task.id, { progress: val });
        });
      });
    </script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
  </body>
</html>
