<!doctype html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8" />
    <title>Comprehensive Table Tree Test</title>
    <link rel="stylesheet" href="../dist/frappe-gantt.css" />
    <style>
        .container { width: 95%; margin: 0 auto; }
        .chart { border: 1px solid #bbb; border-radius: 4px; margin-top: 32px; height: 500px; }
        .controls { margin: 20px 0; }
        .controls button { margin-right: 10px; padding: 8px 16px; }
        .status { margin: 10px 0; padding: 10px; background: #f0f0f0; border-radius: 4px; }
        
        /* 自定义任务颜色 */
        .bar-project .bar { fill: #4f8cff !important; }
        .bar-task .bar { fill: #34c759 !important; }
        .bar-subtask .bar { fill: #ff9500 !important; }
        .bar-milestone .bar { fill: #ff3b30 !important; }
        
        /* 进度条颜色 */
        .bar-project .bar-progress { fill: #1e40af !important; }
        .bar-task .bar-progress { fill: #15803d !important; }
        .bar-subtask .bar-progress { fill: #b45309 !important; }
        .bar-milestone .bar-progress { fill: #dc2626 !important; }
    </style>
    <script src="../dist/frappe-gantt.umd.js"></script>
</head>
<body>
<div class="container">
    <h1>Comprehensive Table Tree Test</h1>
    <p>This test verifies all table tree functionality including data display, scrolling, and arrow behavior.</p>
    
    <div class="status" id="status">
        Status: Initializing...
    </div>
    
    <div class="controls">
        <button id="expand-all-btn">展开所有</button>
        <button id="collapse-all-btn">折叠所有</button>
        <button id="toggle-table-btn">切换表格显示</button>
        <button id="test-scroll-btn">测试滚动</button>
        <button id="test-arrows-btn">测试箭头</button>
    </div>
    
    <div class="chart" id="comprehensive-test"></div>
</div>

<script>
    let gantt;
    let testResults = [];
    
    function updateStatus(message) {
        document.getElementById('status').textContent = 'Status: ' + message;
        console.log('Test Status:', message);
    }
    
    function addTestResult(test, passed, details = '') {
        testResults.push({ test, passed, details });
        console.log(`Test "${test}": ${passed ? 'PASSED' : 'FAILED'}${details ? ' - ' + details : ''}`);
    }
    
    // 测试数据 - 包含层级结构
    const testData = [
        {
            id: 'Project1',
            name: 'Project #1',
            start: '2023-04-02',
            end: '2023-05-04',
            progress: 45,
            custom_class: 'bar-project',
            priority: 'Medium'
        },
        {
            id: 'Task1',
            name: 'Task #1',
            start: '2023-04-03',
            end: '2023-04-08',
            progress: 80,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2',
            name: 'Task #2',
            start: '2023-04-03',
            end: '2023-04-14',
            progress: 30,
            dependencies: 'Project1',
            custom_class: 'bar-task',
            priority: 'High'
        },
        {
            id: 'Task2_1',
            name: 'Task #2.1',
            start: '2023-04-03',
            end: '2023-04-06',
            progress: 100,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Medium'
        },
        {
            id: 'Task2_2',
            name: 'Task #2.2',
            start: '2023-04-06',
            end: '2023-04-09',
            progress: 60,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        {
            id: 'Task2_3',
            name: 'Task #2.3',
            start: '2023-04-10',
            end: '2023-04-16',
            progress: 40,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        {
            id: 'Task2_4',
            name: 'Task #2.4',
            start: '2023-04-10',
            end: '2023-04-16',
            progress: 20,
            dependencies: 'Task2',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        {
            id: 'Task3',
            name: 'Task #3',
            start: '2023-04-02',
            end: '2023-04-08',
            progress: 75,
            custom_class: 'bar-task',
            priority: 'Medium'
        },
        {
            id: 'Task4',
            name: 'Task #4',
            start: '2023-04-03',
            end: '2023-04-15',
            progress: 25,
            custom_class: 'bar-task',
            priority: 'Low'
        },
        {
            id: 'Task4_1',
            name: 'Task #4.1',
            start: '2023-04-03',
            end: '2023-04-07',
            progress: 90,
            dependencies: 'Task4',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        {
            id: 'Task4_2',
            name: 'Task #4.2',
            start: '2023-04-03',
            end: '2023-04-07',
            progress: 70,
            dependencies: 'Task4',
            custom_class: 'bar-subtask',
            priority: 'Low'
        },
        {
            id: 'Milestone1',
            name: 'Mediate milestone',
            start: '2023-04-14',
            end: '2023-04-15',
            progress: 0,
            custom_class: 'bar-milestone',
            priority: 'High'
        },
        {
            id: 'Milestone2',
            name: 'Final milestone',
            start: '2023-04-15',
            end: '2023-04-16',
            progress: 0,
            custom_class: 'bar-milestone',
            priority: 'High'
        }
    ];
    
    try {
        updateStatus('Initializing Gantt chart...');
        
        // 初始化甘特图
        gantt = new Gantt('#comprehensive-test', testData, {
            view_mode: 'Day',
            bar_height: 32,
            bar_corner_radius: 8,
            arrow_curve: 8,
            padding: 20,
            // 启用表格树功能
            table_tree: {
                enabled: true,
                show_table: true,
                table_width: 300,
                indent_width: 20,
                expand_icon: '▶',
                collapse_icon: '▼',
                columns: [
                    { key: 'name', title: 'Task name', width: 150 },
                    { key: 'start', title: 'Start time', width: 80 },
                    { key: 'duration', title: 'Duration', width: 70 }
                ]
            }
        });
        
        updateStatus('Gantt chart initialized successfully');
        addTestResult('Gantt Initialization', true);
        
        // 测试表格数据显示
        setTimeout(() => {
            const tableRows = document.querySelectorAll('.table-row');
            addTestResult('Table Data Display', tableRows.length > 0, `Found ${tableRows.length} table rows`);
            
            const tableCells = document.querySelectorAll('.table-cell');
            addTestResult('Table Cell Content', tableCells.length > 0, `Found ${tableCells.length} table cells`);
            
            updateStatus('Basic tests completed');
        }, 500);
        
    } catch (error) {
        updateStatus('Error: ' + error.message);
        addTestResult('Gantt Initialization', false, error.message);
        console.error('Initialization error:', error);
    }
    
    // 绑定按钮事件
    document.getElementById('expand-all-btn').onclick = () => {
        try {
            updateStatus('Testing expand all...');
            gantt.expand_all();
            addTestResult('Expand All', true);
            updateStatus('Expand all completed');
        } catch (error) {
            addTestResult('Expand All', false, error.message);
            updateStatus('Expand all failed: ' + error.message);
        }
    };
    
    document.getElementById('collapse-all-btn').onclick = () => {
        try {
            updateStatus('Testing collapse all...');
            gantt.collapse_all();
            addTestResult('Collapse All', true);
            updateStatus('Collapse all completed');
        } catch (error) {
            addTestResult('Collapse All', false, error.message);
            updateStatus('Collapse all failed: ' + error.message);
        }
    };
    
    document.getElementById('toggle-table-btn').onclick = () => {
        try {
            updateStatus('Testing table toggle...');
            const currentShow = gantt.options.table_tree.show_table;
            gantt.options.table_tree.show_table = !currentShow;
            gantt.render();
            addTestResult('Table Toggle', true);
            updateStatus('Table toggle completed');
        } catch (error) {
            addTestResult('Table Toggle', false, error.message);
            updateStatus('Table toggle failed: ' + error.message);
        }
    };
    
    document.getElementById('test-scroll-btn').onclick = () => {
        try {
            updateStatus('Testing scroll behavior...');
            const container = document.querySelector('.gantt-container');
            if (container) {
                container.scrollLeft = 200;
                setTimeout(() => {
                    const tableTree = document.querySelector('.table-tree');
                    if (tableTree) {
                        addTestResult('Scroll Behavior', true, 'Table tree position maintained during scroll');
                    } else {
                        addTestResult('Scroll Behavior', false, 'Table tree not found');
                    }
                    updateStatus('Scroll test completed');
                }, 100);
            } else {
                addTestResult('Scroll Behavior', false, 'Container not found');
            }
        } catch (error) {
            addTestResult('Scroll Behavior', false, error.message);
            updateStatus('Scroll test failed: ' + error.message);
        }
    };
    
    document.getElementById('test-arrows-btn').onclick = () => {
        try {
            updateStatus('Testing arrow behavior...');
            const arrows = document.querySelectorAll('.arrow path');
            addTestResult('Arrow Display', arrows.length > 0, `Found ${arrows.length} arrows`);
            updateStatus('Arrow test completed');
        } catch (error) {
            addTestResult('Arrow Display', false, error.message);
            updateStatus('Arrow test failed: ' + error.message);
        }
    };
    
    // 显示测试结果
    setTimeout(() => {
        console.log('\n=== TEST RESULTS ===');
        testResults.forEach(result => {
            console.log(`${result.passed ? '✓' : '✗'} ${result.test}${result.details ? ' - ' + result.details : ''}`);
        });
        console.log('===================\n');
    }, 2000);
</script>
</body>
</html>
