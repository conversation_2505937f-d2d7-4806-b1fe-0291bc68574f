<!DOCTYPE html>
<html lang="zh-cn">
<head>
  <meta charset="UTF-8">
  <title>自定义任务颜色示例 - Frappe Gantt</title>
  <link rel="stylesheet" href="../dist/frappe-gantt.css">
  <style>
    body { font-family: Arial, sans-serif; margin: 40px; background: #f7f7f7; }
    #gantt { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #0001; padding: 24px; }
    .legend { margin-bottom: 16px; }
    .legend span { display: inline-block; width: 16px; height: 16px; margin-right: 8px; border-radius: 3px; vertical-align: middle; }
  </style>
</head>
<body>
  <h2>自定义任务颜色示例</h2>
  <div class="legend">
    <span style="background:#4caf50"></span> 绿色任务
    <span style="background:#2196f3; margin-left:24px;"></span> 蓝色任务
    <span style="background:#ff9800; margin-left:24px;"></span> 橙色任务
  </div>
  <div id="gantt"></div>
  <script src="../dist/frappe-gantt.umd.js"></script>
  <script>
    const tasks = [
      {
        id: 'Task1',
        name: '绿色任务',
        start: '2025-07-01',
        end: '2025-07-05',
        progress: 60,
        color: '#4caf50',
        color_progress: '#388e3c',
      },
      {
        id: 'Task2',
        name: '蓝色任务',
        start: '2025-07-03',
        end: '2025-07-10',
        progress: 30,
        color: '#2196f3',
        color_progress: '#1565c0',
      },
      {
        id: 'Task3',
        name: '橙色任务',
        start: '2025-07-08',
        end: '2025-07-15',
        progress: 80,
        color: '#ff9800',
        color_progress: '#ef6c00',
        dependencies: 'Task1'
      }
    ];
    new Gantt("#gantt", tasks, {
      custom_popup_html: null
    });
  </script>
  <!-- 颜色已通过 color 字段控制，无需额外样式 -->
</body>
</html>
