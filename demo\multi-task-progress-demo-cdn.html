<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <title>Frappe Gantt 多任务进度演示（CDN 版）</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/frappe-gantt/dist/frappe-gantt.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        #gantt { border: 1px solid #ccc; margin-top: 20px; }
    </style>
</head>
<body>
    <h2>Frappe Gantt 多任务进度演示（CDN 版）</h2>
    <div id="gantt"></div>
    <script src="https://cdn.jsdelivr.net/npm/frappe-gantt/dist/frappe-gantt.umd.js"></script>
    <script>
        const tasks = [
            {
                id: 'Task1',
                name: '需求分析',
                start: '2025-07-01',
                end: '2025-07-05',
                progress: 80
            },
            {
                id: 'Task2',
                name: '设计',
                start: '2025-07-03',
                end: '2025-07-10',
                progress: 40
            },
            {
                id: 'Task3',
                name: '开发',
                start: '2025-07-08',
                end: '2025-07-20',
                progress: 10
            },
            {
                id: 'Task4',
                name: '测试',
                start: '2025-07-15',
                end: '2025-07-25',
                progress: 0
            }
        ];
        // CDN 方式全局变量为 Gantt
        const gantt = new Gantt("#gantt", tasks, {
            view_mode: 'Day',
            show_expected_progress: true,
            bar_height: 32,
            column_width: 48
        });
    </script>
</body>
</html>
